// netlify/functions/process.js
// Enhanced text processing function for Netlify with advanced humanization

const { addControlledMistakes, changeStyle, simpleParaphrase } = require('../../src/utils/textModifiers');
const { checkWithGPTZero } = require('../../src/services/gptzeroClient');
const { paraphraseWithPegasus } = require('../../src/services/paraphraseService');
const { balancedHumanization, qualityCheck } = require('../../src/utils/balancedHumanizer');
const { humanizeText } = require('../../src/services/humaneyesService');

exports.handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Content-Type': 'application/json',
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: '',
        };
    }

    // Only allow POST method
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ message: `Method ${event.httpMethod} Not Allowed` }),
        };
    }

    try {
        const { text, styleProfile, styleStrength } = JSON.parse(event.body);

        if (!text || typeof text !== 'string' || !text.trim()) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ message: 'Input text is required and must be a non-empty string.' }),
            };
        }

        // Validate style parameters if provided
        if (styleStrength !== undefined && (typeof styleStrength !== 'number' || styleStrength < 0 || styleStrength > 100)) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ message: 'Style strength must be a number between 0 and 100.' }),
            };
        }

        let modifiedText = text; // Start with the original text

        const startTime = Date.now();

        // --- Step 1: Enhanced Humanization with Advanced LLMs (DeepSeek-R1, Llama 3.1) ---
        console.log("Starting enhanced humanization with advanced models...");

        try {
            // Calculate target aggressiveness based on style strength
            const baseAggressiveness = 0.7;
            const adjustedAggressiveness = styleStrength ?
                Math.min(0.95, baseAggressiveness + (styleStrength / 100) * 0.3) :
                baseAggressiveness;

            // Use the enhanced humanization service with advanced LLMs
            const humanizationResult = await humanizeText(modifiedText, {
                aggressiveness: adjustedAggressiveness,
                maintainTone: true,
                targetDetection: 10, // Target ≤10% AI detection
                method: 'auto', // Use advanced LLMs (DeepSeek-R1) with fallback chain
                fallbackEnabled: true
            });

            if (humanizationResult.success) {
                modifiedText = humanizationResult.humanizedText;
                const processingTime = Date.now() - startTime;
                console.log(`Successfully humanized with ${humanizationResult.actualMethod} method in ${processingTime}ms`);

                // Log model information for debugging
                if (humanizationResult.model) {
                    console.log(`Model used: ${humanizationResult.model}${humanizationResult.provider ? ` (${humanizationResult.provider})` : ''}`);
                }
            } else {
                console.warn(`Enhanced humanization failed: ${humanizationResult.error}. Using fallback methods.`);

                // Fallback to legacy paraphrasing
                const paraphraseResult = await paraphraseWithPegasus(modifiedText);
                if (paraphraseResult && !paraphraseResult.error) {
                    modifiedText = paraphraseResult.paraphrased_text;
                    console.log("Fallback paraphrasing completed.");
                } else {
                    modifiedText = await simpleParaphrase(modifiedText);
                    console.log("Simple paraphrasing fallback completed.");
                }
            }
        } catch (error) {
            console.error('Humanization error:', error.message);

            // Final fallback to simple paraphrasing
            console.log("Applying simple paraphrasing as final fallback...");
            modifiedText = await simpleParaphrase(modifiedText);
        }

        // --- Step 2: Apply enhanced balanced humanization with optional style ---
        console.log("Applying enhanced balanced humanization...");
        if (styleProfile && styleStrength > 0) {
            console.log(`Applying personal style: ${styleProfile.name} at ${styleStrength}% strength`);
            // The balancedHumanization function now handles style integration
            const result = await balancedHumanization(modifiedText, styleProfile, styleStrength, {
                useAdvanced: true,
                aggressiveness: 0.7,
                maintainTone: true
            });
            modifiedText = typeof result === 'string' ? result : await result;
        } else {
            modifiedText = await balancedHumanization(modifiedText, null, 0, {
                useAdvanced: true,
                aggressiveness: 0.7,
                maintainTone: true
            });
        }

        // --- Step 3: Quality check ---
        console.log("Performing quality check...");
        const qualityResult = qualityCheck(modifiedText);

        if (qualityResult.hasIssues) {
            console.log("Quality issues detected:", qualityResult.issues);
            // Apply minimal additional processing if needed
            modifiedText = addControlledMistakes(modifiedText);
        } else {
            console.log("Quality check passed");
        }

        // --- Step 4: Apply subtle style changes ---
        console.log("Applying subtle style changes...");
        modifiedText = changeStyle(modifiedText);

        // --- Step 5: AI Detection Check ---
        console.log("Performing AI detection check...");
        const detectionResult = await checkWithGPTZero(modifiedText);

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ modifiedText, detectionResult }),
        };

    } catch (error) {
        console.error("Error in /api/process:", error);
        const errorMessage = error.message || 'Error processing text.';
        
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                message: errorMessage,
                error: error.toString(),
                detectionResult: {
                    error: true,
                    status: "Server Error",
                    message: "Failed to process text due to an internal server error.",
                    score: null
                }
            }),
        };
    }
};
